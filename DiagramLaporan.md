# FlowCamp UML Diagrams - Guest & Student Features

## Use Case Diagrams

### Guest User Journey
```mermaid
graph TB
    Guest[Guest User]
    
    %% Guest Use Cases
    Guest --> UC1[Browse Home Page]
    Guest --> UC2[View Program Details]
    Guest --> UC3[Browse Bootcamp Information]
    Guest --> UC4[Access Free Classes]
    Guest --> UC5[View Testimonials]
    Guest --> UC6[Register Account]
    Guest --> UC7[Login to System]
    Guest --> UC8[Forgot Password]
    Guest --> UC9[Email Verification]
    Guest --> UC10[View Course Information]
    Guest --> UC11[Learn More About Programs]
    Guest --> UC12[Contact via WhatsApp]
    
    %% System Components
    UC1 --> SYS1[Home Page System]
    UC2 --> SYS2[Program Management System]
    UC3 --> SYS3[Bootcamp Information System]
    UC4 --> SYS4[Free Class System]
    UC5 --> SYS5[Testimonial System]
    UC6 --> SYS6[Registration System]
    UC7 --> SYS7[Authentication System]
    UC8 --> SYS7
    UC9 --> SYS8[Email Verification System]
    UC10 --> SYS9[Course Information System]
    UC11 --> SYS2
    UC12 --> SYS10[Communication System]
    
    %% Relationships
    UC6 -.-> UC9 : includes
    UC7 --> REDIRECT[Redirect to Student Dashboard]
    UC8 -.-> UC9 : includes
```

### Student Learning Management
```mermaid
graph TB
    Student[Student User]
    
    %% Learning Management Use Cases
    Student --> UC1[View Dashboard]
    Student --> UC2[Browse Available Classes]
    Student --> UC3[View Class Details]
    Student --> UC4[Enroll in Classes]
    Student --> UC5[Access Learning Materials]
    Student --> UC6[Complete Assignments]
    Student --> UC7[Submit Tasks]
    Student --> UC8[Track Progress]
    Student --> UC9[View Certificates]
    Student --> UC10[Download Certificates]
    Student --> UC11[View Class Leaderboard]
    Student --> UC12[Access Class Materials]
    
    %% System Components
    UC1 --> SYS1[Dashboard System]
    UC2 --> SYS2[Class Management System]
    UC3 --> SYS2
    UC4 --> SYS2
    UC5 --> SYS3[Learning Material System]
    UC6 --> SYS4[Assignment System]
    UC7 --> SYS4
    UC8 --> SYS5[Progress Tracking System]
    UC9 --> SYS6[Certificate System]
    UC10 --> SYS6
    UC11 --> SYS7[Leaderboard System]
    UC12 --> SYS3
    
    %% Relationships
    UC4 -.-> UC5 : includes
    UC5 -.-> UC6 : includes
    UC6 -.-> UC7 : includes
    UC8 -.-> UC9 : includes
    UC9 -.-> UC10 : includes
```

### Student Profile & Settings
```mermaid
graph TB
    Student[Student User]
    
    %% Profile & Settings Use Cases
    Student --> UC1[View Profile]
    Student --> UC2[Edit Profile Information]
    Student --> UC3[Upload Profile Picture]
    Student --> UC4[Change Password]
    Student --> UC5[Manage Notifications]
    Student --> UC6[View Notification History]
    Student --> UC7[Clear Notifications]
    Student --> UC8[Update Account Settings]
    Student --> UC9[Delete Account]
    Student --> UC10[Submit Testimonials]
    Student --> UC11[Rate Classes]
    Student --> UC12[Provide Feedback]
    
    %% System Components
    UC1 --> SYS1[Profile Management System]
    UC2 --> SYS1
    UC3 --> SYS2[Image Upload System]
    UC4 --> SYS3[Security System]
    UC5 --> SYS4[Notification System]
    UC6 --> SYS4
    UC7 --> SYS4
    UC8 --> SYS1
    UC9 --> SYS3
    UC10 --> SYS5[Testimonial System]
    UC11 --> SYS6[Rating System]
    UC12 --> SYS7[Feedback System]
    
    %% Storage Systems
    SYS1 --> STORAGE1[localStorage Profile Data]
    SYS2 --> STORAGE1
    SYS4 --> STORAGE2[localStorage Notifications]
    SYS5 --> STORAGE3[localStorage Testimonials]
    
    %% Relationships
    UC2 -.-> UC3 : includes
    UC5 -.-> UC6 : includes
    UC6 -.-> UC7 : includes
    UC10 -.-> UC11 : includes
    UC11 -.-> UC12 : includes
```

### Student Navigation & UI
```mermaid
graph TB
    Student[Student User]
    
    %% Navigation & UI Use Cases
    Student --> UC1[Toggle Sidebar]
    Student --> UC2[Navigate Between Pages]
    Student --> UC3[Search Classes]
    Student --> UC4[Filter Content]
    Student --> UC5[Sort Data]
    Student --> UC6[Paginate Results]
    Student --> UC7[View Responsive Layout]
    Student --> UC8[Access Mobile Interface]
    Student --> UC9[Use Keyboard Navigation]
    Student --> UC10[View Breadcrumbs]
    Student --> UC11[Access Help System]
    Student --> UC12[Logout from System]
    
    %% UI Components
    UC1 --> COMP1[Sidebar Component]
    UC2 --> COMP2[Router Navigation]
    UC3 --> COMP3[Search Component]
    UC4 --> COMP4[Filter Component]
    UC5 --> COMP5[Sort Component]
    UC6 --> COMP6[Pagination Component]
    UC7 --> COMP7[Responsive Layout System]
    UC8 --> COMP7
    UC9 --> COMP8[Accessibility System]
    UC10 --> COMP9[Breadcrumb Component]
    UC11 --> COMP10[Help System]
    UC12 --> COMP11[Authentication System]
    
    %% State Management
    COMP1 --> STATE1[Sidebar State]
    COMP2 --> STATE2[Router State]
    COMP3 --> STATE3[Search State]
    COMP4 --> STATE4[Filter State]
    COMP5 --> STATE5[Sort State]
    COMP6 --> STATE6[Pagination State]
    
    %% Relationships
    UC1 -.-> UC7 : affects
    UC2 -.-> UC10 : includes
    UC3 -.-> UC4 : includes
    UC4 -.-> UC5 : includes
    UC5 -.-> UC6 : includes
```

## Activity Diagrams

### Guest Authentication to Student Dashboard
```mermaid
flowchart TD
    Start([Guest Visits Site]) --> ViewHome[View Home Page]
    ViewHome --> Decision1{Want to Login?}
    
    Decision1 -->|No| BrowseContent[Browse Public Content]
    BrowseContent --> ViewPrograms[View Programs]
    ViewPrograms --> ViewBootcamp[View Bootcamp Info]
    ViewBootcamp --> ViewFreeClass[View Free Classes]
    ViewFreeClass --> ViewTestimonials[View Testimonials]
    ViewTestimonials --> Decision2{Ready to Join?}
    Decision2 -->|No| End1([Continue Browsing])
    Decision2 -->|Yes| GoToRegister[Go to Register]
    
    Decision1 -->|Yes| GoToLogin[Navigate to Login Page]
    GoToRegister --> FillRegisterForm[Fill Registration Form]
    FillRegisterForm --> SubmitRegister[Submit Registration]
    SubmitRegister --> EmailVerification[Email Verification Process]
    EmailVerification --> VerifyEmail[Verify Email Code]
    VerifyEmail --> LoginAfterRegister[Login with New Account]
    
    GoToLogin --> FillLoginForm[Fill Login Form]
    LoginAfterRegister --> FillLoginForm
    FillLoginForm --> ValidateCredentials[Validate Credentials]
    ValidateCredentials --> Decision3{Valid Credentials?}
    
    Decision3 -->|No| ShowError[Show Error Message]
    ShowError --> FillLoginForm
    
    Decision3 -->|Yes| AuthSuccess[Authentication Success]
    AuthSuccess --> RedirectToDashboard[Redirect to Student Dashboard]
    RedirectToDashboard --> LoadStudentData[Load Student Data]
    LoadStudentData --> InitializeSidebar[Initialize Sidebar State]
    InitializeSidebar --> LoadNotifications[Load Notifications]
    LoadNotifications --> DisplayDashboard[Display Student Dashboard]
    DisplayDashboard --> End2([Student Session Active])
```

### Student Class Enrollment and Progress
```mermaid
flowchart TD
    Start([Student Accesses Classes]) --> ViewAvailableClasses[View Available Classes]
    ViewAvailableClasses --> BrowseClasses[Browse Class List]
    BrowseClasses --> Decision1{Found Interesting Class?}

    Decision1 -->|No| SearchClasses[Search/Filter Classes]
    SearchClasses --> BrowseClasses

    Decision1 -->|Yes| ViewClassDetail[View Class Details]
    ViewClassDetail --> ReviewClassInfo[Review Class Information]
    ReviewClassInfo --> CheckPrerequisites[Check Prerequisites]
    CheckPrerequisites --> Decision2{Meet Requirements?}

    Decision2 -->|No| ViewOtherClasses[View Other Classes]
    ViewOtherClasses --> BrowseClasses

    Decision2 -->|Yes| EnrollInClass[Enroll in Class]
    EnrollInClass --> AddToStudiedClasses[Add to Studied Classes]
    AddToStudiedClasses --> NavigateToAcademy[Navigate to Academy]
    NavigateToAcademy --> ViewClassProgress[View Class Progress]

    ViewClassProgress --> AccessMaterials[Access Learning Materials]
    AccessMaterials --> ReadMaterial[Read Material Content]
    ReadMaterial --> MarkAsRead[Mark Material as Read]
    MarkAsRead --> Decision3{Has Assignment?}

    Decision3 -->|No| UpdateProgress[Update Progress]
    Decision3 -->|Yes| ViewAssignment[View Assignment Details]
    ViewAssignment --> CompleteAssignment[Complete Assignment]
    CompleteAssignment --> SubmitAssignment[Submit Assignment]
    SubmitAssignment --> UpdateTaskStatus[Update Task Status]
    UpdateTaskStatus --> UpdateProgress

    UpdateProgress --> CalculateOverallProgress[Calculate Overall Progress]
    CalculateOverallProgress --> Decision4{Progress = 100%?}

    Decision4 -->|No| ContinueLearning[Continue Learning]
    ContinueLearning --> AccessMaterials

    Decision4 -->|Yes| CompleteClass[Mark Class as Completed]
    CompleteClass --> GenerateCertificate[Generate Certificate]
    GenerateCertificate --> NotifyCompletion[Notify Completion]
    NotifyCompletion --> End([Class Completed])
```

### Assignment Submission Process
```mermaid
flowchart TD
    Start([Student Views Assignment]) --> CheckAssignmentStatus[Check Assignment Status]
    CheckAssignmentStatus --> Decision1{Assignment Status?}

    Decision1 -->|Pending| ViewAssignmentDetails[View Assignment Details]
    Decision1 -->|Past Due| ShowPastDueWarning[Show Past Due Warning]
    Decision1 -->|Submitted| ViewSubmissionHistory[View Submission History]
    Decision1 -->|Completed| ViewFeedback[View Feedback & Score]

    ShowPastDueWarning --> ViewAssignmentDetails
    ViewAssignmentDetails --> ReadInstructions[Read Assignment Instructions]
    ReadInstructions --> CheckDueDate[Check Due Date]
    CheckDueDate --> Decision2{Time Remaining?}

    Decision2 -->|No Time| ShowUrgentWarning[Show Urgent Warning]
    Decision2 -->|Some Time| StartAssignment[Start Working on Assignment]
    ShowUrgentWarning --> StartAssignment

    StartAssignment --> WorkOnAssignment[Work on Assignment]
    WorkOnAssignment --> SaveProgress[Save Progress Locally]
    SaveProgress --> Decision3{Ready to Submit?}

    Decision3 -->|No| ContinueWorking[Continue Working]
    ContinueWorking --> WorkOnAssignment

    Decision3 -->|Yes| ReviewSubmission[Review Submission]
    ReviewSubmission --> ValidateSubmission[Validate Submission]
    ValidateSubmission --> Decision4{Valid Submission?}

    Decision4 -->|No| ShowValidationError[Show Validation Error]
    ShowValidationError --> WorkOnAssignment

    Decision4 -->|Yes| SubmitAssignment[Submit Assignment]
    SubmitAssignment --> UpdateTaskStatus[Update Task Status to Submitted]
    UpdateTaskStatus --> SaveSubmissionData[Save Submission Data]
    SaveSubmissionData --> ShowSuccessMessage[Show Success Message]
    ShowSuccessMessage --> UpdateNotifications[Update Notifications]
    UpdateNotifications --> UpdateProgress[Update Class Progress]
    UpdateProgress --> End([Assignment Submitted])

    ViewSubmissionHistory --> ShowSubmissionDetails[Show Submission Details]
    ShowSubmissionDetails --> Decision5{Want to Resubmit?}
    Decision5 -->|Yes| StartAssignment
    Decision5 -->|No| End

    ViewFeedback --> ShowScore[Show Score & Comments]
    ShowScore --> End
```

### Certificate Generation and Viewing
```mermaid
flowchart TD
    Start([Student Completes Class]) --> CheckProgress[Check Class Progress]
    CheckProgress --> Decision1{Progress = 100%?}

    Decision1 -->|No| ShowIncompleteMessage[Show Incomplete Message]
    ShowIncompleteMessage --> RedirectToClass[Redirect to Class Materials]
    RedirectToClass --> End1([Continue Learning])

    Decision1 -->|Yes| GenerateCertificate[Generate Certificate]
    GenerateCertificate --> CreateCertificateData[Create Certificate Data]
    CreateCertificateData --> SetCertificateURL[Set Certificate Image URL]
    SetCertificateURL --> UpdateClassStatus[Update Class Status to Completed]
    UpdateClassStatus --> SaveCertificateInfo[Save Certificate Information]
    SaveCertificateInfo --> AddToCompletedClasses[Add to Completed Classes]

    AddToCompletedClasses --> NavigateToCertificates[Navigate to Certificates Page]
    NavigateToCertificates --> DisplayCertificateList[Display Certificate List]
    DisplayCertificateList --> SelectCertificate[Select Certificate to View]
    SelectCertificate --> OpenCertificateModal[Open Certificate Modal]

    OpenCertificateModal --> LoadCertificateImage[Load Certificate Image]
    LoadCertificateImage --> Decision2{Image Loaded Successfully?}

    Decision2 -->|No| ShowImageError[Show Image Error]
    ShowImageError --> ShowFallbackMessage[Show Fallback Message]
    ShowFallbackMessage --> DisplayCertificateInfo[Display Certificate Info]

    Decision2 -->|Yes| DisplayCertificateImage[Display Certificate Image]
    DisplayCertificateImage --> DisplayCertificateInfo
    DisplayCertificateInfo --> ShowDownloadButton[Show Download Button]

    ShowDownloadButton --> Decision3{Want to Download?}
    Decision3 -->|No| ViewCertificate[View Certificate]
    Decision3 -->|Yes| DownloadCertificate[Download Certificate]

    DownloadCertificate --> CreateDownloadLink[Create Download Link]
    CreateDownloadLink --> TriggerDownload[Trigger Download]
    TriggerDownload --> ShowDownloadSuccess[Show Download Success]
    ShowDownloadSuccess --> MarkAsDownloaded[Mark as Downloaded]
    MarkAsDownloaded --> ViewCertificate

    ViewCertificate --> Decision4{Close Certificate?}
    Decision4 -->|No| ViewCertificate
    Decision4 -->|Yes| CloseCertificateModal[Close Certificate Modal]
    CloseCertificateModal --> End2([Certificate Viewed])
```

### Profile Settings and Notification Management
```mermaid
flowchart TD
    Start([Student Accesses Settings]) --> LoadSettingsPage[Load Settings Page]
    LoadSettingsPage --> LoadUserProfile[Load User Profile from localStorage]
    LoadUserProfile --> DisplayProfileForm[Display Profile Form]
    DisplayProfileForm --> Decision1{What to Update?}

    Decision1 -->|Profile Info| EditProfileInfo[Edit Profile Information]
    Decision1 -->|Profile Picture| UploadProfilePicture[Upload Profile Picture]
    Decision1 -->|Password| ChangePassword[Change Password]
    Decision1 -->|Notifications| ManageNotifications[Manage Notifications]

    EditProfileInfo --> FillProfileForm[Fill Profile Form]
    FillProfileForm --> ValidateProfileData[Validate Profile Data]
    ValidateProfileData --> Decision2{Valid Data?}
    Decision2 -->|No| ShowValidationError[Show Validation Error]
    ShowValidationError --> FillProfileForm
    Decision2 -->|Yes| SaveProfileData[Save Profile Data to localStorage]
    SaveProfileData --> EmitProfileUpdate[Emit Profile Update Event]
    EmitProfileUpdate --> ShowSuccessMessage[Show Success Message]

    UploadProfilePicture --> SelectImageFile[Select Image File]
    SelectImageFile --> ValidateImageFile[Validate Image File]
    ValidateImageFile --> Decision3{Valid Image?}
    Decision3 -->|No| ShowImageError[Show Image Error]
    ShowImageError --> SelectImageFile
    Decision3 -->|Yes| ShowImagePreview[Show Image Preview]
    ShowImagePreview --> Decision4{Confirm Upload?}
    Decision4 -->|No| CancelUpload[Cancel Upload]
    Decision4 -->|Yes| SaveImageToProfile[Save Image to Profile]
    SaveImageToProfile --> UpdateAvatarInComponents[Update Avatar in Components]
    UpdateAvatarInComponents --> ShowSuccessMessage

    ChangePassword --> FillPasswordForm[Fill Password Form]
    FillPasswordForm --> ValidatePasswords[Validate Passwords]
    ValidatePasswords --> Decision5{Valid Passwords?}
    Decision5 -->|No| ShowPasswordError[Show Password Error]
    ShowPasswordError --> FillPasswordForm
    Decision5 -->|Yes| UpdatePassword[Update Password]
    UpdatePassword --> ShowSuccessMessage

    ManageNotifications --> ViewNotificationList[View Notification List]
    ViewNotificationList --> Decision6{Action on Notification?}
    Decision6 -->|Clear Single| ClearSingleNotification[Clear Single Notification]
    Decision6 -->|Clear All| ClearAllNotifications[Clear All Notifications]
    Decision6 -->|View Details| ViewNotificationDetails[View Notification Details]

    ClearSingleNotification --> UpdateClearedList[Update Cleared Notifications List]
    ClearAllNotifications --> UpdateClearedList
    UpdateClearedList --> SaveToLocalStorage[Save to localStorage]
    SaveToLocalStorage --> RefreshNotificationList[Refresh Notification List]

    ViewNotificationDetails --> NavigateToRelatedPage[Navigate to Related Page]
    NavigateToRelatedPage --> End1([Navigate Away])

    ShowSuccessMessage --> RefreshComponents[Refresh Related Components]
    RefreshComponents --> End2([Settings Updated])
    CancelUpload --> End2
    RefreshNotificationList --> End2
```

## Sequence Diagrams

### Guest Login to Student Dashboard
```mermaid
sequenceDiagram
    participant Guest as Guest User
    participant LoginPage as LoginPage.vue
    participant Router as Vue Router
    participant Auth as Authentication System
    participant Storage as localStorage
    participant Dashboard as StudentDashboard.vue
    participant Navbar as NavbarStudent.vue
    participant Sidebar as Sidebar.vue

    Guest->>LoginPage: Navigate to /login
    LoginPage->>LoginPage: Display login form
    Guest->>LoginPage: Fill email and password
    Guest->>LoginPage: Click "Sign In"

    LoginPage->>LoginPage: Validate form data
    alt Valid credentials
        LoginPage->>Auth: Authenticate user
        Auth-->>LoginPage: Authentication success
        LoginPage->>Router: router.push('/')
        Router->>Dashboard: Navigate to StudentDashboard

        Dashboard->>Storage: Load class data from localStorage
        Storage-->>Dashboard: Return stored classes
        Dashboard->>Dashboard: Initialize dashboard statistics
        Dashboard->>Dashboard: Generate assignment data
        Dashboard->>Dashboard: Create progress charts

        Dashboard->>Navbar: Mount NavbarStudent component
        Navbar->>Storage: Load user profile
        Navbar->>Storage: Load notifications
        Storage-->>Navbar: Return profile and notifications
        Navbar->>Navbar: Initialize notification system

        Dashboard->>Sidebar: Mount Sidebar component
        Sidebar->>Storage: Load sidebar state
        Sidebar->>Storage: Load user profile
        Storage-->>Sidebar: Return sidebar state and profile

        Dashboard-->>Guest: Display student dashboard
    else Invalid credentials
        LoginPage->>LoginPage: Show error message
        LoginPage-->>Guest: Display login error
    end
```

### Student Dashboard Data Loading
```mermaid
sequenceDiagram
    participant Student as Student User
    participant Dashboard as StudentDashboard.vue
    participant ClassStore as useClassStore
    participant Storage as localStorage
    participant Utils as studentUtils
    participant Chart as Chart.js

    Student->>Dashboard: Access /student/dashboard
    Dashboard->>Dashboard: Set isLoading = true

    Dashboard->>ClassStore: Get classes data
    ClassStore->>Storage: Retrieve 'flowcamp-classes'
    Storage-->>ClassStore: Return stored classes
    ClassStore-->>Dashboard: Return classes array

    Dashboard->>Dashboard: Calculate completed classes
    Dashboard->>Dashboard: Calculate total lessons
    Dashboard->>Dashboard: Calculate completed lessons
    Dashboard->>Dashboard: Calculate overall progress

    Dashboard->>Utils: generateAssignmentData(classes)
    Utils-->>Dashboard: Return assignment data

    Dashboard->>Utils: generateAssignmentResults(classes)
    Utils-->>Dashboard: Return assignment results

    Dashboard->>Dashboard: Prepare chart data
    Dashboard->>Chart: Initialize progress chart
    Chart-->>Dashboard: Chart instance created

    Dashboard->>Dashboard: Set isLoading = false
    Dashboard-->>Student: Display dashboard with data

    Note over Dashboard: Dashboard updates every time<br/>student navigates to page
```

### Class Progress Tracking
```mermaid
sequenceDiagram
    participant Student as Student User
    participant ClassDetail as DetailClass1.vue
    participant ClassStore as useClassStore
    participant Storage as localStorage
    participant Material as LearningMaterials.vue
    participant Router as Vue Router

    Student->>ClassDetail: Access class detail page
    ClassDetail->>ClassStore: setCurrentClass(classId)
    ClassStore->>Storage: Save current class ID
    ClassStore-->>ClassDetail: Return class data

    Student->>ClassDetail: Click "View Materials"
    ClassDetail->>Router: Navigate to materials page
    Router->>Material: Load LearningMaterials.vue

    Material->>ClassStore: Get current class materials
    ClassStore-->>Material: Return materials array
    Material-->>Student: Display materials list

    Student->>Material: Click on material
    Material->>ClassStore: setCurrentMaterial(materialId)
    ClassStore->>Storage: Save current material ID

    Student->>Material: Read material content
    Material->>ClassStore: markMaterialAsRead(materialId)
    ClassStore->>ClassStore: Update material.isRead = true
    ClassStore->>Storage: Save updated classes
    ClassStore->>ClassStore: calculateProgress(classId)

    alt Material has task
        Student->>Material: Complete assignment
        Material->>ClassStore: updateTaskStatus(materialId, 'submitted')
        ClassStore->>ClassStore: Update material.taskStatus
        ClassStore->>Storage: Save updated classes
        ClassStore->>ClassStore: calculateProgress(classId)
    end

    ClassStore->>ClassStore: Check if progress = 100%
    alt Progress = 100%
        ClassStore->>ClassStore: updateClassStatus(classId, 'completed')
        ClassStore->>ClassStore: Generate certificate
        ClassStore->>Storage: Save completed class
    end

    ClassStore-->>Material: Return updated progress
    Material-->>Student: Display updated progress
```

### Notification System Management
```mermaid
sequenceDiagram
    participant Student as Student User
    participant Navbar as NavbarStudent.vue
    participant Storage as localStorage
    participant ClassStore as useClassStore
    participant Utils as studentUtils

    Navbar->>Storage: Load cleared notifications
    Storage-->>Navbar: Return cleared notification IDs

    Navbar->>ClassStore: Get classes data
    ClassStore-->>Navbar: Return classes with materials

    Navbar->>Navbar: Generate notifications from class data
    loop For each class material with task
        Navbar->>Navbar: Check task status and due date
        alt Task needs attention
            Navbar->>Navbar: Create notification object
            Navbar->>Navbar: Check if notification was cleared
            alt Not cleared
                Navbar->>Navbar: Add to notifications array
            end
        end
    end

    Navbar->>Storage: Save notifications to localStorage
    Navbar->>Navbar: Sort notifications (unread first)
    Navbar-->>Student: Display notification count in bell icon

    Student->>Navbar: Click notification bell
    Navbar->>Navbar: Toggle notification dropdown
    Navbar-->>Student: Display notification list

    Student->>Navbar: Click on notification
    Navbar->>Navbar: Mark notification as read
    Navbar->>Storage: Save updated notifications
    Navbar->>Router: Navigate to related page

    Student->>Navbar: Click clear notification (X)
    Navbar->>Navbar: Add to cleared notifications list
    Navbar->>Storage: Save cleared notifications
    Navbar->>Navbar: Remove from current notifications
    Navbar->>Storage: Save updated notifications
    Navbar-->>Student: Update notification display

    Student->>Navbar: Click "Clear All"
    Navbar->>Navbar: Add all notifications to cleared list
    Navbar->>Storage: Save cleared notifications
    Navbar->>Navbar: Clear notifications array
    Navbar->>Storage: Save empty notifications
    Navbar-->>Student: Hide notification dropdown
```

### Profile Settings Updates
```mermaid
sequenceDiagram
    participant Student as Student User
    participant Settings as StudentSettings.vue
    participant Storage as localStorage
    participant Navbar as NavbarStudent.vue
    participant Sidebar as Sidebar.vue
    participant Window as Window Events

    Student->>Settings: Access /student/settings
    Settings->>Storage: Load user profile data
    Storage-->>Settings: Return profile object
    Settings-->>Student: Display profile form

    Student->>Settings: Update profile information
    Settings->>Settings: Validate form data
    alt Valid data
        Settings->>Storage: Save updated profile
        Settings->>Window: Dispatch 'userProfileUpdated' event
        Window->>Navbar: Receive profile update event
        Navbar->>Navbar: Update userName and userAvatar
        Window->>Sidebar: Receive profile update event
        Sidebar->>Sidebar: Update userName and userAvatar
        Settings-->>Student: Show success message
    else Invalid data
        Settings-->>Student: Show validation errors
    end

    Student->>Settings: Upload profile picture
    Settings->>Settings: Validate image file
    alt Valid image
        Settings->>Settings: Create image preview
        Settings-->>Student: Show image preview modal
        Student->>Settings: Confirm upload
        Settings->>Settings: Update profile.avatar
        Settings->>Storage: Save updated profile
        Settings->>Window: Dispatch 'userProfileUpdated' event
        Window->>Navbar: Update avatar display
        Window->>Sidebar: Update avatar display
        Settings-->>Student: Show success message
    else Invalid image
        Settings-->>Student: Show image error
    end

    Student->>Settings: Change password
    Settings->>Settings: Validate password form
    alt Valid passwords
        Settings->>Settings: Simulate password update
        Settings->>Storage: Save password change flag
        Settings-->>Student: Show success message
    else Invalid passwords
        Settings-->>Student: Show password errors
    end

    Note over Settings,Sidebar: Profile updates are synchronized<br/>across all components using events
```
